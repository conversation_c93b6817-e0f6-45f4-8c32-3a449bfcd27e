"""
TravelPlannerAgent LangGraph工作流图

使用LangGraph构建的旅行规划Agent工作流，支持双模运行和状态管理。
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import Travel<PERSON>lanState, ProcessingStage, PlanningMode, create_initial_state, add_event_to_state
from .nodes import (
    core_intent_analyzer_node,
    multi_city_strategy_node,
    driving_context_analyzer_node,
    preference_analyzer_node,
    should_analyze_multi_city,
    should_analyze_driving_context,
    has_error
)

logger = logging.getLogger(__name__)


class TravelPlannerGraph:
    """旅行规划Agent图形类"""
    
    def __init__(self):
        """初始化图形"""
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = MemorySaver()
        self._build_graph()
    
    def _build_graph(self):
        """构建工作流图"""
        logger.info("开始构建TravelPlannerAgent工作流图")
        
        # 创建状态图
        workflow = StateGraph(TravelPlanState)
        
        # 添加节点
        workflow.add_node("core_intent_analyzer", core_intent_analyzer_node)
        workflow.add_node("multi_city_strategy", multi_city_strategy_node)
        workflow.add_node("driving_context_analyzer", driving_context_analyzer_node)
        workflow.add_node("preference_analyzer", preference_analyzer_node)
        workflow.add_node("itinerary_generator", self._itinerary_generator_node)
        workflow.add_node("optimizer", self._optimizer_node)
        workflow.add_node("error_handler", self._error_handler_node)
        
        # 设置入口点
        workflow.set_entry_point("core_intent_analyzer")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "core_intent_analyzer",
            should_analyze_multi_city,
            {
                "multi_city_strategy": "multi_city_strategy",
                "driving_context": "driving_context_analyzer"
            }
        )
        
        workflow.add_conditional_edges(
            "multi_city_strategy",
            should_analyze_driving_context,
            {
                "driving_context": "driving_context_analyzer",
                "preference_analysis": "preference_analyzer"
            }
        )
        
        workflow.add_conditional_edges(
            "driving_context_analyzer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "preference_analyzer"
            }
        )
        
        workflow.add_conditional_edges(
            "preference_analyzer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "itinerary_generator"
            }
        )
        
        workflow.add_conditional_edges(
            "itinerary_generator",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "optimizer"
            }
        )
        
        workflow.add_conditional_edges(
            "optimizer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": END
            }
        )
        
        # 错误处理节点直接结束
        workflow.add_edge("error_handler", END)
        
        # 编译图形
        self.graph = workflow
        self.compiled_graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("TravelPlannerAgent工作流图构建完成")
    
    async def _itinerary_generator_node(self, state: TravelPlanState) -> TravelPlanState:
        """行程生成节点"""
        logger.info(f"开始行程生成 - Session: {state['session_id']}")
        
        try:
            # 添加开始事件
            state = add_event_to_state(state, "stage_start", {
                "stage": "itinerary_generation",
                "message": "正在为您生成详细的旅行行程..."
            })
            
            # 导入分析服务
            from ..services.analysis_service import AnalysisService
            analysis_service = AnalysisService()
            
            # 生成行程
            itinerary = await analysis_service.generate_itinerary(
                core_intent=state.get("core_intent", {}),
                preference_profile=state.get("preference_profile", {}),
                multi_city_strategy=state.get("multi_city_strategy"),
                driving_context=state.get("driving_context"),
                planning_mode=state.get("planning_mode", "general_assistance")
            )
            
            # 解析每日行程
            daily_itineraries = itinerary.get("daily_itineraries", [])
            state["daily_itineraries"] = daily_itineraries
            
            # 保存行程摘要
            state["summary"] = itinerary.get("itinerary_summary", {})
            
            # 保存推荐信息
            state["recommendations"] = itinerary.get("recommendations", {})
            
            # 记录工具调用
            state["tool_calls"].append({
                "node": "itinerary_generator",
                "timestamp": datetime.now().isoformat(),
                "input": {
                    "planning_mode": state.get("planning_mode"),
                    "destinations": state.get("core_intent", {}).get("destinations"),
                    "days": state.get("core_intent", {}).get("days")
                },
                "output": {
                    "daily_count": len(daily_itineraries),
                    "total_attractions": sum(
                        len([item for item in day.get("items", []) if item.get("type") == "attraction"])
                        for day in daily_itineraries
                    )
                }
            })
            
            # 添加完成事件
            state = add_event_to_state(state, "itinerary_generated", {
                "daily_count": len(daily_itineraries),
                "summary": state["summary"]
            })
            
            logger.info(f"行程生成完成 - 生成了 {len(daily_itineraries)} 天的行程")
            
        except Exception as e:
            logger.error(f"行程生成失败: {str(e)}")
            state["has_error"] = True
            state["error_message"] = f"行程生成失败: {str(e)}"
            state = add_event_to_state(state, "error", {
                "stage": "itinerary_generation",
                "error": str(e)
            })
        
        return state
    
    async def _optimizer_node(self, state: TravelPlanState) -> TravelPlanState:
        """优化节点"""
        logger.info(f"开始行程优化 - Session: {state['session_id']}")
        
        try:
            # 添加开始事件
            state = add_event_to_state(state, "stage_start", {
                "stage": "optimization",
                "message": "正在优化您的行程安排..."
            })
            
            # 这里可以添加行程优化逻辑
            # 例如：路线优化、时间调整、成本优化等
            
            # 标记为完成
            state["is_completed"] = True
            state["current_stage"] = ProcessingStage.COMPLETED.value
            state["updated_at"] = datetime.now().isoformat()
            
            # 添加完成事件
            state = add_event_to_state(state, "planning_completed", {
                "message": "旅行规划已完成！",
                "summary": state.get("summary", {}),
                "total_days": len(state.get("daily_itineraries", []))
            })
            
            logger.info(f"行程优化完成 - Session: {state['session_id']}")
            
        except Exception as e:
            logger.error(f"行程优化失败: {str(e)}")
            state["has_error"] = True
            state["error_message"] = f"行程优化失败: {str(e)}"
            state = add_event_to_state(state, "error", {
                "stage": "optimization",
                "error": str(e)
            })
        
        return state
    
    async def _error_handler_node(self, state: TravelPlanState) -> TravelPlanState:
        """错误处理节点"""
        logger.error(f"进入错误处理 - Session: {state['session_id']}")
        
        # 设置错误状态
        state["current_stage"] = ProcessingStage.ERROR.value
        state["is_completed"] = True
        state["updated_at"] = datetime.now().isoformat()
        
        # 添加错误事件
        state = add_event_to_state(state, "planning_failed", {
            "error": state.get("error_message", "未知错误"),
            "stage": state.get("current_stage", "unknown")
        })
        
        return state
    
    async def run(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        运行旅行规划工作流
        
        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）
            
        Returns:
            规划结果
        """
        try:
            # 生成会话ID
            if not session_id:
                session_id = f"travel_plan_{user_id}_{int(datetime.now().timestamp())}"
            
            logger.info(f"开始运行旅行规划工作流 - Session: {session_id}")
            
            # 创建初始状态
            initial_state = create_initial_state(
                session_id=session_id,
                user_id=user_id,
                original_query=original_query,
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )
            
            # 运行工作流
            config = {"configurable": {"thread_id": session_id}}
            
            final_state = await self.compiled_graph.ainvoke(
                initial_state,
                config=config
            )
            
            logger.info(f"旅行规划工作流完成 - Session: {session_id}")
            return final_state
            
        except Exception as e:
            logger.error(f"旅行规划工作流失败 - Session: {session_id}, 错误: {str(e)}")
            raise
    
    async def stream_run(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ):
        """
        流式运行旅行规划工作流
        
        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）
            
        Yields:
            状态更新流
        """
        try:
            # 生成会话ID
            if not session_id:
                session_id = f"travel_plan_{user_id}_{int(datetime.now().timestamp())}"
            
            logger.info(f"开始流式运行旅行规划工作流 - Session: {session_id}")
            
            # 创建初始状态
            initial_state = create_initial_state(
                session_id=session_id,
                user_id=user_id,
                original_query=original_query,
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )
            
            # 流式运行工作流
            config = {"configurable": {"thread_id": session_id}}
            
            async for chunk in self.compiled_graph.astream(
                initial_state,
                config=config
            ):
                yield chunk
            
            logger.info(f"流式旅行规划工作流完成 - Session: {session_id}")
            
        except Exception as e:
            logger.error(f"流式旅行规划工作流失败 - Session: {session_id}, 错误: {str(e)}")
            raise
    
    async def get_state_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        获取状态历史
        
        Args:
            session_id: 会话ID
            
        Returns:
            状态历史列表
        """
        try:
            config = {"configurable": {"thread_id": session_id}}
            history = []
            
            async for state in self.compiled_graph.aget_state_history(config):
                history.append(state.values)
            
            return history
            
        except Exception as e:
            logger.error(f"获取状态历史失败 - Session: {session_id}, 错误: {str(e)}")
            return []
    
    def get_graph_visualization(self) -> str:
        """
        获取图形可视化
        
        Returns:
            Mermaid格式的图形定义
        """
        mermaid_graph = """
        graph TD
            A[core_intent_analyzer] --> B{多城市?}
            B -->|是| C[multi_city_strategy]
            B -->|否| D[driving_context_analyzer]
            C --> E{自驾?}
            E -->|是| D
            E -->|否| F[preference_analyzer]
            D --> G{有错误?}
            G -->|是| H[error_handler]
            G -->|否| F
            F --> I{有错误?}
            I -->|是| H
            I -->|否| J[itinerary_generator]
            J --> K{有错误?}
            K -->|是| H
            K -->|否| L[optimizer]
            L --> M{有错误?}
            M -->|是| H
            M -->|否| N[END]
            H --> N
        """
        return mermaid_graph
